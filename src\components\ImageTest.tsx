import React, { useState } from 'react';
import { X } from 'lucide-react';

// Simple test component to debug image loading issues
const ImageTest: React.FC = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  
  const testImages = [
    '/product-pictures/1 (1).png',
    '/product-pictures/1 (2).png',
    '/product-pictures/1 (3).png',
    '/product-pictures/1 (4).png'
  ];

  return (
    <div className="p-8 bg-gray-900 min-h-screen">
      <h1 className="text-white text-2xl mb-8">Image Loading Test</h1>
      
      {/* Test Gallery Grid */}
      <div className="grid grid-cols-2 gap-4 mb-8">
        {testImages.map((src, index) => (
          <div key={index} className="border border-gray-600 p-4">
            <h3 className="text-white mb-2">Test Image {index + 1}</h3>
            
            {/* Direct img tag test */}
            <div className="mb-4">
              <p className="text-gray-400 text-sm mb-2">Direct img tag:</p>
              <img 
                src={src} 
                alt={`Test ${index + 1}`}
                className="w-full h-32 object-cover border border-gray-500"
                onLoad={() => console.log(`✅ Direct img loaded: ${src}`)}
                onError={() => console.error(`❌ Direct img failed: ${src}`)}
              />
            </div>
            
            {/* Encoded URL test */}
            <div className="mb-4">
              <p className="text-gray-400 text-sm mb-2">Encoded URL:</p>
              <img 
                src={src.replace(/ /g, '%20')} 
                alt={`Test encoded ${index + 1}`}
                className="w-full h-32 object-cover border border-gray-500"
                onLoad={() => console.log(`✅ Encoded img loaded: ${src.replace(/ /g, '%20')}`)}
                onError={() => console.error(`❌ Encoded img failed: ${src.replace(/ /g, '%20')}`)}
              />
            </div>
            
            {/* Click to open modal test */}
            <button 
              onClick={() => {
                console.log(`🖱️ Opening modal for: ${src}`);
                setSelectedImage(src);
              }}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Open in Modal
            </button>
          </div>
        ))}
      </div>

      {/* Simple Modal Test */}
      {selectedImage && (
        <div 
          className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div 
            className="relative max-w-4xl max-h-full bg-gray-800 p-4 rounded-lg"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              onClick={() => setSelectedImage(null)}
              className="absolute top-2 right-2 text-white hover:text-red-400 p-2"
            >
              <X size={24} />
            </button>
            
            <h2 className="text-white mb-4">Modal Image Test</h2>
            <p className="text-gray-400 mb-4">Source: {selectedImage}</p>
            
            {/* Test different loading approaches */}
            <div className="space-y-4">
              <div>
                <p className="text-gray-400 text-sm mb-2">Direct src:</p>
                <img 
                  src={selectedImage}
                  alt="Modal test direct"
                  className="max-w-full max-h-96 object-contain border border-gray-600"
                  onLoad={() => console.log(`✅ Modal direct loaded: ${selectedImage}`)}
                  onError={() => console.error(`❌ Modal direct failed: ${selectedImage}`)}
                />
              </div>
              
              <div>
                <p className="text-gray-400 text-sm mb-2">Encoded src:</p>
                <img 
                  src={selectedImage.replace(/ /g, '%20')}
                  alt="Modal test encoded"
                  className="max-w-full max-h-96 object-contain border border-gray-600"
                  onLoad={() => console.log(`✅ Modal encoded loaded: ${selectedImage.replace(/ /g, '%20')}`)}
                  onError={() => console.error(`❌ Modal encoded failed: ${selectedImage.replace(/ /g, '%20')}`)}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageTest;
