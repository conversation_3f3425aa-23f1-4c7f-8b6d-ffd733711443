
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" href="/newlogofinal.png" type="image/png" />
    <title>Econic Media | Web Design & Product Photography</title>
    <meta name="description" content="Econic Media - Modern web design and professional product photography for ambitious brands" />
    <meta name="author" content="Econic Media" />
    <meta name="keywords" content="web design, product photography, web development, SEO services, branding, UI/UX design, Berlin web design, professional photography" />

    <!-- Google Site Verification -->
    <meta name="google-site-verification" content="MWPFku2-TD_V1Stmutb3rwA6RlkCsyKHgLkefHLnoNc" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Econic Media | Web Design & Product Photography" />
    <meta property="og:description" content="Modern web design and professional product photography for ambitious brands" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://www.econicmedia.pro/" />
    <meta property="og:image" content="https://www.econicmedia.pro/websitepreview.png" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:image:alt" content="Econic Media - Modern web design and professional product photography for ambitious brands" />
    <meta property="og:site_name" content="Econic Media" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@EconicMedia" />
    <meta name="twitter:title" content="Econic Media | Web Design & Product Photography" />
    <meta name="twitter:description" content="Modern web design and professional product photography for ambitious brands" />
    <meta name="twitter:image" content="https://www.econicmedia.pro/websitepreview.png" />
    <meta name="twitter:image:alt" content="Econic Media - Modern web design and professional product photography for ambitious brands" />

    <!-- Additional Meta Tags for Better SEO -->
    <meta name="robots" content="index, follow" />
    <meta name="theme-color" content="#0a0a0a" />
    <link rel="canonical" href="https://www.econicmedia.pro/" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&family=Poppins:wght@400;500;600;700;800;900&display=swap" media="print" onload="this.media='all'">
    <noscript>
      <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&family=Poppins:wght@400;500;600;700;800;900&display=swap">
    </noscript>
    <style data-critical>
      /* Critical font optimization */
      .font-black, h1, h2, h4, h5, h6, .section-title {
        font-weight: 700 !important;
        -webkit-text-stroke: 0.3px currentColor !important;
        text-shadow: 0 0 1px currentColor !important;
        -webkit-font-smoothing: antialiased;
      }
      .card-title, h3 {
        font-weight: 600 !important;
        -webkit-text-stroke: 0.2px currentColor !important;
        -webkit-font-smoothing: antialiased;
      }
      /* Performance-first font loading */
      @font-face {
        font-family: 'Inter';
        font-display: swap;
      }
    </style>
    <link rel="preload" href="/assets/css/main.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="/assets/css/main.css"></noscript>

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "LocalBusiness",
      "name": "Econic Media",
      "image": "https://www.econicmedia.pro/newlogofinal.png",
      "@id": "https://www.econicmedia.pro/",
      "url": "https://www.econicmedia.pro/",
      "telephone": "+491571234567",
      "priceRange": "$$$",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Example Street 1",
        "addressLocality": "Berlin",
        "postalCode": "10115",
        "addressCountry": "DE"
      },
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": 52.520008,
        "longitude": 13.404954
      },
      "openingHoursSpecification": {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": [
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday"
        ],
        "opens": "09:00",
        "closes": "18:00"
      },
      "sameAs": [
        "https://www.facebook.com/econicmedia",
        "https://www.twitter.com/econicmedia",
        "https://www.instagram.com/econicmedia"
      ] 
    }
    </script>
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "url": "https://www.econicmedia.pro/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://www.econicmedia.pro/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>
  </head>

  <body>
    <div id="root"></div>
    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <!-- Application entry point - use standard path for Vite -->
    <script type="module" src="/src/main.tsx"></script>

    <!-- Google Analytics -->
    <script type="text/partytown" async src="https://www.googletagmanager.com/gtag/js?id=G-9D73GZV2X9"></script>
    <script type="text/partytown">
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}

      // Set default consent to 'denied'
      gtag('consent', 'default', {
        'analytics_storage': 'denied',
        'ad_storage': 'denied'
      });

      gtag('js', new Date());
      gtag('config', 'G-9D73GZV2X9');
    </script>
  </body>
</html>
