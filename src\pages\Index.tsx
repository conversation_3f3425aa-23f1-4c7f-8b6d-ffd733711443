import React, { Suspense, lazy } from 'react';
import { Helmet } from 'react-helmet-async';
import { LanguageProvider } from '@/context/LanguageContext';
import { useScrollReveal } from '@/hooks/useScrollReveal';

import Navbar from '@/components/Navbar';
import HeroSection from '@/components/HeroSection';
import Footer from '@/components/Footer';

// Lazy load sections for better performance
const ExpertiseSection = lazy(() => import('@/components/BenefitsSection'));
const GallerySection = lazy(() => import('@/components/GallerySection'));
const PortfolioSection = lazy(() => import('@/components/PortfolioSection'));
const PricingSection = lazy(() => import('@/components/PricingSection'));
const TestimonialsSection = lazy(() => import('@/components/TestimonialsSection'));
const ContactSection = lazy(() => import('@/components/ContactSection'));

// Loading fallback component for sections
const SectionLoader = () => (
  <div className="flex items-center justify-center py-20">
    <div className="w-8 h-8 border-2 border-neon-cyan/20 border-t-neon-cyan rounded-full animate-spin"></div>
  </div>
);

const Index = () => {
  useScrollReveal();

  return (
    <LanguageProvider>
      <Helmet>
        <title>Econic Media | Web Design, Web Development & Product Photography</title>
        <meta name="description" content="Econic Media offers professional web design, web development, and product photography services to elevate your brand. We build stunning, high-performance websites and create captivating visuals that drive engagement and sales." />
        <meta name="keywords" content="web design, web development, product photography, SEO, branding, UI/UX, e-commerce websites, Berlin, Germany, professional photography, web agency" />
        <link rel="canonical" href="https://econicmedia.pro" />
        <meta property="og:title" content="Econic Media | Web Design, Web Development & Product Photography" />
        <meta property="og:description" content="Econic Media offers professional web design, web development, and product photography services to elevate your brand. We build stunning, high-performance websites and create captivating visuals that drive engagement and sales." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://econicmedia.pro" />
        <meta property="og:image" content="https://econicmedia.pro/lovable-uploads/f71aca91-6b27-4100-a40e-883ce27ec690.png" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Econic Media | Web Design, Web Development & Product Photography" />
        <meta name="twitter:description" content="Econic Media offers professional web design, web development, and product photography services to elevate your brand. We build stunning, high-performance websites and create captivating visuals that drive engagement and sales." />
        <meta name="twitter:image" content="https://econicmedia.pro/lovable-uploads/f71aca91-6b27-4100-a40e-883ce27ec690.png" />
      </Helmet>
      <div className="min-h-screen bg-background text-foreground">
        <Navbar />

        <HeroSection />

        <Suspense fallback={<SectionLoader />}>
          <ExpertiseSection />
        </Suspense>
        
        <Suspense fallback={<SectionLoader />}>
          <GallerySection />
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <PortfolioSection />
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <PricingSection />
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <TestimonialsSection />
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <ContactSection />
        </Suspense>

        <Footer />
      </div>
    </LanguageProvider>
  );
};

export default Index;
